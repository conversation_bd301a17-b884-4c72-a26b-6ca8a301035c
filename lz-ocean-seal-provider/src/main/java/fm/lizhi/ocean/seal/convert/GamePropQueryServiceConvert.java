package fm.lizhi.ocean.seal.convert;

import fm.lizhi.ocean.seal.constant.GamePropGrantStatus;
import fm.lizhi.ocean.seal.pojo.GamePropQueryResult;
import fm.lizhi.ocean.seal.pojo.UserPropStatusResult;
import fm.lizhi.ocean.seal.pojo.PropGrantStatusResult;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * 游戏道具查询服务转换器
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {GamePropGrantStatus.class, Date.class}, uses = {CommonConvert.class}

)
public interface GamePropQueryServiceConvert {

    GamePropQueryServiceConvert INSTANCE = Mappers.getMapper(GamePropQueryServiceConvert.class);

    /**
     * 转换用户道具状态信息列表到 Proto 消息列表
     */
    List<GamePropServiceProto.UserPropStatus> convertUserPropStatusInfoListToProtoList(List<UserPropStatusResult.UserPropStatusInfo> userPropStatusInfoList);

    /**
     * 转换用户道具状态信息到 Proto 消息
     */
    @Mapping(target = "unknownFields", ignore = true)
    @Mapping(target = "propIdBytes", ignore = true)
    @Mapping(target = "mergeFrom", ignore = true)
    @Mapping(target = "defaultInstanceForType", ignore = true)
    @Mapping(target = "allFields", ignore = true)
    @Mapping(target = "mergeUnknownFields", ignore = true)
    @Mapping(target = "clearField", ignore = true)
    GamePropServiceProto.UserPropStatus convertUserPropStatusInfoToProto(UserPropStatusResult.UserPropStatusInfo userPropStatusInfo);

    /**
     * 转换道具发放状态信息到 Proto 消息
     */
    @Mapping(target = "userIdBytes", ignore = true)
    @Mapping(target = "removeDetails", ignore = true)
    @Mapping(target = "gameIdBytes", ignore = true)
    @Mapping(target = "detailsOrBuilderList", ignore = true)
    @Mapping(target = "detailsBuilderList", ignore = true)
    @Mapping(target = "unknownFields", ignore = true)
    @Mapping(target = "mergeFrom", ignore = true)
    @Mapping(target = "defaultInstanceForType", ignore = true)
    @Mapping(target = "allFields", ignore = true)
    @Mapping(target = "mergeUnknownFields", ignore = true)
    @Mapping(target = "clearField", ignore = true)
    @Mapping(target = "appId", source = "param.appId")
    @Mapping(target = "detailsList", source = "propGrantStatusInfo.details")
    @Mapping(target = "gameId", source = "param.channelGameId")
    @Mapping(target = "uniqueId", source = "param.uniqueId")
    GamePropServiceProto.PropGrantStatusInfo convertPropGrantStatusInfoToProto(PropGrantStatusResult.PropGrantStatusInfo propGrantStatusInfo, GamePropServiceProto.QueryPropGrantStatusParam param);

    @Mapping(target = "unknownFields", ignore = true)
    @Mapping(target = "propIdBytes", ignore = true)
    @Mapping(target = "mergeFrom", ignore = true)
    @Mapping(target = "defaultInstanceForType", ignore = true)
    @Mapping(target = "allFields", ignore = true)
    @Mapping(target = "mergeUnknownFields", ignore = true)
    @Mapping(target = "clearField", ignore = true)
    GamePropServiceProto.PropGrantDetail convertPropGrantDetailInfoToProto(PropGrantStatusResult.PropGrantDetailInfo propGrantDetailInfo);

}
