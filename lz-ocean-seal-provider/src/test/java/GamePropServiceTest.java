import cn.hutool.core.lang.UUID;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.ocean.seal.api.GamePropService;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.GamePropType;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto;
import io.github.cfgametech.Response;
import io.github.cfgametech.SDK;
import io.github.cfgametech.beans.GetChannelTokenRequest;
import io.github.cfgametech.beans.GetChannelTokenResponse;
import io.github.cfgametech.beans.GetGameServiceListRequest;
import io.github.cfgametech.beans.GetGameServiceListResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 */
@Slf4j
public class GamePropServiceTest {

    static {
//        -Dmetadata.region=cn
//                -Dmetadata.deploy.env=test
//                -Dmetadata.business.env=lizhi
//                -Dmetadata.service.name=lz_ocean_seal
//                -Dconf.env=office
//                -Dconf.key=lz_ocean_seal
//                -Dapp.name=lz_ocean_seal
//                -Dregion=cn
//                -DbusinessEnv=lizhi
//                -DCAT_HOME=/tmp
        System.setProperty("metadata.region", "cn");
        System.setProperty("metadata.deploy.env", "test");
        System.setProperty("metadata.service.name", "lz_ocean_seal_local");
        System.setProperty("conf.env", "cn");
        System.setProperty("region", "cn");
        System.setProperty("businessEnv", "lizhi");

    }

    public static final GamePropService gamePropService = ProxyBuilderContainer.PROXY_BUILDER.buildProxy(GamePropService.class);


    @Test
    public void grantGameProp() throws Exception {
        GamePropServiceProto.GrantGamePropParam.Builder builder = GamePropServiceProto.GrantGamePropParam.newBuilder();
        builder.setUserId(1431178837848312194L);
        builder.setChannelPropId("1");
        builder.setNum(1);
        builder.setUniqueId(UUID.fastUUID().toString());
        builder.setAppId("1895023737905397760");
        builder.setGameId("102");
        builder.setChannel(GameChannel.LUK);
        builder.setDuration(3600L);

        Result<GamePropServiceProto.ResponseGrantGameProp> result = gamePropService.grantGameProp(builder.build());
        log.info("rCode: {}", JsonUtil.dumps(result.rCode()));
        log.info("target: {}", JsonUtil.dumps(result.target()));
    }


    @Test
    public void getGamePropList() throws Exception {
        GamePropServiceProto.GetGamePropListParam.Builder builder = GamePropServiceProto.GetGamePropListParam.newBuilder();
        builder.setChannelGameId("102");
        builder.setType(GamePropType.PROP.getType());
        builder.setAppId("1895023737905397760");
        builder.setPageNumber(1);
        builder.setPageSize(10);

        Result<GamePropServiceProto.ResponseGetGamePropList> result = gamePropService.getGamePropList(builder.build());
        log.info("rCode: {}", JsonUtil.dumps(result.rCode()));
        log.info("target: {}", JsonUtil.dumps(result.target().getGamePropsList()));
    }

    
}
